import sys
project_root = '/wkspace/qiushi/ltv-prediction/src'
sys.path.append(str(project_root))

import os
import numpy as np
import pytorch_lightning as pl
from dataset.light_data_payment_buckets import PaymentBucketsDataModule
from sklearn.model_selection import train_test_split
import tarfile
import pandas as pd
from io import StringIO
from collections import defaultdict

from config import config
from dataset.load_data import Fish3DLTVDataset


def load_campaign_data(filepath):
    """ 读取用户的广告数据 """
    campaign_df = pd.read_csv(filepath)
    return campaign_df


def load_behavior_data(filepaths):
    """ 读取用户的行为数据 """
    df_list = []
    for fp in filepaths:
        if fp.endswith('csv'):
            df = pd.read_csv(fp)
            df_list.append(df)
        else:
            with tarfile.open(fp, 'r:gz') as tar:
                for member in tar.getmembers():
                    if member.name.endswith('.csv'):
                        csv_file = tar.extractfile(member)
                        content = csv_file.read().decode('utf-8') 
                        df = pd.read_csv(StringIO(content)) 
                        print(f"Loaded: {member.name}")
                        df_list.append(df)

    df_combined = pd.concat(df_list, ignore_index=True)
    return df_combined




def analyze_campaign_column(df, verbose=True):
    """ 区分列名
    ignore_cols: 忽略
    X_cols: 输入特征
    y_cols: 输出目标
    key_cols: ID等信息
    """
    ignore_cols, X_cols, y_cols = [], [], []
    key_cols = []
    for column in df.columns:
        distinct_vals = df[column].nunique()
        missing_vals = df[column].isna().sum()
        if column in ('sdk_yidun_device_id', 'attribution_day', 'adtrace_aid', 'proj_main_channel', 
                      'channel_ty_adgroup_id', 'channel_ty_account_id', 'channel_ty_campaign_id', 'channel_ty_csite_id'):
            key_cols.append(column)
        elif distinct_vals in (0, 1) or missing_vals == df.shape[0]:  # 全部相同 或 全部缺失
            ignore_cols.append(column)
        elif df[column].dtype == 'object' and (distinct_vals + missing_vals > 0.7 * df.shape[0] or distinct_vals > 0.2 * df.shape[0]):  # obj类型过多
            ignore_cols.append(column) 
        elif column in ('ingest_time', 'event_time', 'sdk_sysfiletime', 'adtrace_last_active_time', 'adtrace_missionid', 'adtrace_attribution_time', 
                        'adtrace_pay_times_boundary', 'sdk_boottimeinsec', 'adtrace_yidun_last_active_time'): # ignore time
            ignore_cols.append(column)
        elif column in ('adtrace_yidun_validate_message', ): #  人工过滤
            ignore_cols.append(column)
        elif column.endswith('retention') or column.endswith('payment'):
            if column[:3] in ('d1_', 'd2_', 'd3_'):
                X_cols.append(column)
            elif column[:3] in ('d4_', 'd5_', 'd6_', 'd7_'):
                y_cols.append(column)
            else:
                ignore_cols.append(column)
        else:
            if verbose:
                print(f"Column: {column}")
                print(f"  Data type: {df[column].dtype}")
                print(f"  Distinct values: {distinct_vals}")
                print(f"  Missing values: {missing_vals}")
                print(f"  sample: ")
                print(f"{df[column].head(5)}")
                print("-" * 40)  # 分隔线
            X_cols.append(column)
    return key_cols, X_cols, y_cols, ignore_cols


def add_device_price(df):
    import pandas as pd
    device2price = pd.read_csv(os.path.join(project_root, '../data/device2price.csv'))
    df_with_price = pd.merge(
        df,
        device2price,
        left_on='sdk_device_name',
        right_on='device_name', 
        how='left' 
    )
    df_with_price = df_with_price.rename(columns={'price': 'device_price'})
    return df_with_price


# print(ltv_dataset.df.shape)
# ignores, valids, keys = analyze_column(ltv_dataset.df)
# print(ignores)
# print(f"len(valids): {len(valids)}, {valids}")





def flatten_behavior_data(df, df_with_attribution):
    user2attribution = df_with_attribution.set_index('sdk_yidun_device_id')['attribution_day'].to_dict()
    df['attribution_day'] = df['sdk_yidun_device_id'].map(user2attribution)
    df['attribution_day'] = pd.to_datetime(df['attribution_day'])
    df['day'] = pd.to_datetime(df['day'])
    df_filter = df[(df['attribution_day'] <= df['day']) & (df['attribution_day'] + pd.Timedelta(days=2) >= df['day'])]
    df_filter['day_diff'] = (df_filter['day'] - df_filter['attribution_day']).dt.days
    # print(df_filter.columns)
    value_columns = ['fish_gun_fire_sum_count', 'gun_level_up_consume_count', 
                     'recharge_count', 'fish_table_enter_count', 'skill_use_count',
                       'bkrpt_count', 'total_recharge_amount', 'login_count',
                       'shop_center_enter_count', 'achievement_reward_count',
                       'have_checkin_reward', 'startup_quest_finish_game_count',
                       'click_120215_count', 'click_120214_count', 'click_120093_count',
                       'click_120092_count', 'resource_total_down_count',
                       'resource_down_count', 'activity_midnight', 'activity_morning',
                       'activity_afternoon', 'activity_night', 
                       'game_time_in_minutes', 'final_delta', 'max_delta', 'min_delta',
                       'max_gunlevel', 'min_gunlevel', 'max_boss_rate', 'total_catch',
                       'total_catch_boss',]

    data_dict = defaultdict(dict)
    for _, row in df_filter.iterrows():
        key = (row['sdk_yidun_device_id'], row['attribution_day'])
        day_diff = row['day_diff']
        
        # 存储每个 day_diff 对应的属性值
        if key not in data_dict:
            data_dict[key] = {'sdk_yidun_device_id': row['sdk_yidun_device_id'], 'attribution_day': row['attribution_day'].strftime('%Y-%m-%d')}
            data_dict[key].update({"behav." + col + f'_{i}': 0 for i in range(3) for col in value_columns })
        
        # 生成后缀 col_0, col_1
        for col in value_columns:
            col_name = f"behav.{col}_{day_diff}"
            data_dict[key][col_name] = row[col]

    df_result = pd.DataFrame(list(data_dict.values()))
    return df_result


def merge_behavior_campaign_data(_behavior_df, _campaign_df):
    print("广告：", _campaign_df.shape)
    print("行为：", _behavior_df.shape)
    full_df = pd.merge(_campaign_df, _behavior_df, on=('sdk_yidun_device_id', 'attribution_day'), how='left')
    print("广告 & 行为：", full_df.shape)
    return full_df
    

# res = flatten_behavior_data(behavior_df, campaign_df)
# behavior_df = load_behavior_data()
# campaign_df = ltv_dataset.df
# print(campaign_df)



import pandas as pd

def generate_train_test_data(df, mode, verbose=True, payment_only=False, split_time=None, fix_negative=True):
    """
    df: pd dataframe
    mode: 'classification', 'regression'
    verbose: 打印信息
    payment_only: 只处理付费用户
    split_time: str, 区分train和test数据的日期，test包含split_time, None则随机
    """
    future_payment_cols = [f'd{i}_payment' for i in range(4, 31)]
    future_retention_cols = [f'd{i}_retention' for i in range(4, 31)]
    payment_label_cols = [f'd{i}_payment' for i in range(4, 8)]
    retention_label_cols = [f'd{i}_retention' for i in range(4, 8)]
    drop_cols = ['sdk_yidun_device_id', 'attribution_day', 'attribution_day_a', 'channel_ty_account_id', 'channel_ty_adgroup_id', 'channel_ty_campaign_id']
    
    full_columns = df.columns.tolist()
    X_columns = [c for c in full_columns if c not in future_payment_cols + future_retention_cols]
    X = df[X_columns]
    
    if fix_negative:
        y = df[payment_label_cols].sum(axis=1).clip(lower=0)
    else:
        y = df[payment_label_cols].sum(axis=1)
    
    obj_features = X.select_dtypes(include=['object']).columns.tolist()
    int_features = X.select_dtypes(include=['int']).columns.tolist()
    float_features = X.select_dtypes(include=['float']).columns.tolist()

    # 处理缺失值、分桶
    X[obj_features] = X[obj_features].fillna('unknown').astype(str)
    X[int_features] = X[int_features].fillna(-1).astype('int64')
    X[float_features] = X[float_features].fillna(-1).astype('float64')

    if verbose:
        for column in X.columns:
            distinct_vals = X[column].nunique()
            missing_vals = X[column].isna().sum()
            print(f"Column: {column}")
            print(f"  Data type: {df[column].dtype}")
            print(f"  Distinct values: {distinct_vals}")
            print(f"  Missing values: {missing_vals}")
            print(f"  sample: ")
            print(f"{X[column].head(5)}")
            print("-" * 40)  # 分隔线

    # 只返回付费用户cha
    if payment_only:
        non_zero_mask = y > 0
        X = X[non_zero_mask]
        y = y[non_zero_mask]
    print(f"{X.shape}, {y.shape}")

    # 分布统计
    print("原始y值分布统计：")
    print(pd.Series(y).describe(percentiles=[0.1, 0.25, 0.4, 0.5, 0.6, 0.75, 0.9]))
    if mode == 'classification':
        print("分类标签分布（%）：")
        # y = [0 if future_payment <= 6 else 1 if future_payment <= 50 else 2 if future_payment <= 500 else 3 for future_payment in y]
        y = y.apply(lambda tt: 0 if tt <= 6 else 1 if tt <= 50 else 2 if tt <= 500 else 3)
        label_distribution = pd.Series(y).value_counts(normalize=True) * 100
        print(label_distribution)

    if split_time is None:
        train_X, test_X, train_y, test_y = train_test_split(X, y, test_size=0.2, random_state=42)
    else:
        mask = X['attribution_day'] < split_time
        train_X, train_y = X[mask], y[mask]
        test_X, test_y = X[~mask], y[~mask]
        
    train_id_list = train_X[['sdk_yidun_device_id', 'attribution_day',  'channel_ty_account_id', 'channel_ty_adgroup_id', 'channel_ty_campaign_id']].values.tolist()
    test_id_list = test_X[['sdk_yidun_device_id', 'attribution_day',  'channel_ty_account_id', 'channel_ty_adgroup_id', 'channel_ty_campaign_id']].values.tolist()
            
    train_X.drop(columns=drop_cols, inplace=True)
    test_X.drop(columns=drop_cols, inplace=True)
    
    return train_X, test_X, train_y, test_y, train_id_list, test_id_list


# ignores, valids, keys = analyze_column(ltv_dataset.df, verbose=False)
# generate_train_test_data(ltv_dataset.df[valids + keys], verbose=False)


from catboost import CatBoostClassifier, CatBoostRegressor
from sklearn.metrics import accuracy_score, confusion_matrix, classification_report
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score


def training(train_X, test_X, train_y, test_y, mode, **kwargs):
    # 定义模型
    if mode == 'classification':
        model = CatBoostClassifier(
            iterations=kwargs.get('iterations', 200),
            learning_rate=kwargs.get('lr', 1e-1),
            depth=kwargs.get('depth', 6),
            eval_metric=kwargs.get('eval_metric', 'Accuracy'),
            loss_function=kwargs.get('loss_function', 'MultiClass'), 
            early_stopping_rounds=kwargs.get('early_stopping_rounds', 50),
            cat_features=kwargs.get('cat_features', []),
            text_features=kwargs.get('text_features', []),
            ignored_features=kwargs.get('ignored_features', []),
            classes_count=kwargs.get('classes_count', 4),
            random_seed=kwargs.get('random_seed', 42),
            l2_leaf_reg=kwargs.get('l2_leaf_reg', 100.0),
            min_data_in_leaf=kwargs.get('min_data_in_leaf', 5),
            verbose=10,
            thread_count=40,
        )
    elif mode == 'regression':
        model = CatBoostRegressor(
            iterations=kwargs.get('iterations', 200),
            learning_rate=kwargs.get('lr', 1e-1),
            depth=kwargs.get('depth', 6),
            eval_metric=kwargs.get('eval_metric', 'MAE'),
            loss_function=kwargs.get('loss_function', 'MAE'), 
            early_stopping_rounds=kwargs.get('early_stopping_rounds', 50),
            cat_features=kwargs.get('cat_features', []),
            text_features=kwargs.get('text_features', []),
            ignored_features=kwargs.get('ignored_features', []),
            random_seed=kwargs.get('random_seed', 42),
            l2_leaf_reg=kwargs.get('l2_leaf_reg', 100.0),
            min_data_in_leaf=kwargs.get('min_data_in_leaf', 5),
            verbose=10,
            thread_count=60,
        )
    else:
        print("mode not defined")
        return
        
    # 训练模型
    model.fit(
        train_X, train_y,
        eval_set=(test_X, test_y),
    )

    # 特征重要性
    print(model.get_params())
    feat_importance = model.get_feature_importance(prettified=True)
    feat_importance = feat_importance.sort_values(by='Importances', ascending=False)
    feat_importance.columns = ['Feature', 'Importance'] 
    feature_types = {
        'Feature': model.feature_names_,
        'Data Type': ['numeric' if f not in model.get_cat_feature_indices() else 'categorical' 
                     for f in range(len(model.feature_names_))],
        'Is CatFeature': [f in model.get_cat_feature_indices() 
                         for f in range(len(model.feature_names_))]
    }
    feature_types_df = pd.DataFrame(feature_types)
    feature_analysis = pd.merge(
        feat_importance,
        feature_types_df,
        on='Feature',
        how='left'
    )
    print("\nFeature Analysis:")
    print(feature_analysis.to_string(index=False))

    return model


import matplotlib.pyplot as plt
from pprint import pprint

def check_df(_df):
    print(_df.shape)
    print(_df.columns)
    print(_df.dtypes)
    print(_df.head(5))

def check_df_data(_df):
    for column in _df.columns:
        print(f"Column: {column}")
        print(f"  Data type: {_df[column].dtype}")
        print(f"  sample: ")
        print(f"{_df[column].head(5)}")
        print("-" * 40)  

def plot_output(true_y, pred_y):
    plt.figure(figsize=(12, 6))
    
    true_y_log = np.log1p(true_y)
    pred_y_log = np.log1p(pred_y)
    
    plt.subplot(1, 2, 1)
    plt.hist(true_y_log, bins=50, color='blue', alpha=0.7, label='True Y (Log Scale)')
    plt.title('Distribution of True Y (Log Scale)')
    plt.xlabel('Log(1 + Y)')
    plt.ylabel('Frequency')
    plt.yscale('log')
    plt.legend()
    plt.grid(which='both', linestyle='--', linewidth=0.5)
    
    plt.subplot(1, 2, 2)
    plt.hist(pred_y_log, bins=50, color='orange', alpha=0.7, label='Predicted Y (Log Scale)')
    plt.title('Distribution of Predicted Y (Log Scale)')
    plt.xlabel('Log(1 + Pred Y)')
    plt.ylabel('Frequency')
    plt.yscale('log')
    plt.legend()
    plt.grid(which='both', linestyle='--', linewidth=0.5)

    # plt.subplot(1, 3, 3)
    # plt.hist(train_y_log, bins=50, color='green', alpha=0.7, label='Training Y (Log Scale)')
    # plt.title('Distribution of Training Y (Log Scale)')
    # plt.xlabel('Log(1 + Training Y)')
    # plt.ylabel('Frequency')
    # plt.yscale('log')
    # plt.legend()
    # plt.grid(which='both', linestyle='--', linewidth=0.5)

    plt.tight_layout()
    plt.show()

def merge_campaign(test_id_list, test_y, pred_y, ignore_compaigns):
    """
    test_id_list: [['sdk_yidun_device_id', 'attribution_day',  'channel_ty_account_id', 'channel_ty_adgroup_id', 'channel_ty_campaign_id'], ...]
    return:
        
    """
    test_y = np.asarray(test_y)
    pred_y = np.asarray(pred_y)
    campaign_ltv_truth = defaultdict(float)
    campaign_ltv_pred = defaultdict(float)
    campaign_counts = defaultdict(int)
    
    for i in range(len(test_id_list)):
        sdk_yidun_device_id, attribution_day,  channel_ty_account_id, channel_ty_adgroup_id, channel_ty_campaign_id = test_id_list[i]
        if channel_ty_campaign_id not in ignore_compaigns:
            campaign_ltv_truth[channel_ty_campaign_id] += test_y[i]
            campaign_ltv_pred[channel_ty_campaign_id] += pred_y[i]
            campaign_counts[channel_ty_campaign_id] += 1

    campaigns = list(campaign_ltv_truth.keys())
    test_y_campaign = [campaign_ltv_truth[c] for c in campaigns]
    pred_y_campaign = [campaign_ltv_pred[c] for c in campaigns]
    counts_campaign = [campaign_counts[c] for c in campaigns]

    s = sorted(zip(test_y_campaign, pred_y_campaign, counts_campaign, campaigns), key=lambda tmp: tmp[2], reverse=True)
    pprint(s[:10])
    
    return test_y_campaign, pred_y_campaign, counts_campaign


def evaluation(test_y, pred_y, mode, name, verbose=False):
    test_y = np.asarray(test_y)
    pred_y = np.asarray(pred_y)
    # pred_y_baseline = np.asarray(pred_y_baseline)

    # 结果统计
    if mode == 'classification':
        print(f"===== {name}")
        print(f"Accuracy: {accuracy_score(test_y, pred_y):.4f}")
        print(f"confusion_matrix: ")
        print(confusion_matrix(test_y, pred_y))
        print(f"classification_report: ")
        print(classification_report(test_y, pred_y, digits=4))
    else:
        print(f"===== {name}")
        print(f"RMSE: {np.sqrt(mean_squared_error(test_y, pred_y)):.4f}")
        print(f"MAE: {mean_absolute_error(test_y, pred_y):.4f}")
        print(f"R² Score: {r2_score(test_y, pred_y):.4f}")
    if verbose:
        pprint(list(zip(test_y.tolist(), pred_y.tolist()))[:30])
    plot_output(test_y, pred_y)



from catboost import CatBoost, CatBoostRegressor
import numpy as np

class ZeroInflatedMAEObjective:
    def calc_ders_range(self, approxes, targets, weights):
        # approxes: 模型预测值（需同时包含分类和回归信息）
        # 假设 approxes 前半部分为付费概率，后半部分为付费金额预测
        n_samples = len(targets)
        paid_proba = approxes[:n_samples]
        paid_amount_pred = approxes[n_samples:]

        # 分类损失梯度（Log Loss）
        ders_class = []
        for p, y in zip(paid_proba, targets):
            if y > 0:
                der1_class = p - 1  # 付费样本的真实标签为1
            else:
                der1_class = p      # 未付费样本的真实标签为0
            ders_class.append((der1_class, 0.0))  # 一阶导和二阶导

        # 回归损失梯度（MAE，仅对付费样本）
        ders_reg = []
        for a, y in zip(paid_amount_pred, targets):
            if y > 0:
                der1_reg = np.sign(a - y)  # MAE梯度
            else:
                der1_reg = 0.0
            ders_reg.append((der1_reg, 0.0))

        # 合并梯度
        ders = ders_class + ders_reg
        return ders

# 模型初始化（需调整参数）
# model = CatBoostRegressor(
#     loss_function=ZeroInflatedMAEObjective(),
#     ignored_features=['adtrace_aid', 'adtrace_act_name', 'channel_ty_word_id', 'adtrace_device_ua', 'click_time', 'sdk_fileinittime'],  
#     cat_features=cat_features, text_features=text_features,
#     iterations=200,
#     learning_rate=0.01,
#     depth=5,
#     verbose=10
# )

# # 数据需构造为多目标格式（此处为示例，需根据实际数据调整）
# y_multi = np.hstack([(train_y > 0).values.reshape(-1,1).astype(int), train_y.values.reshape(-1,1)])  # 多目标
# model.fit(train_X, y_multi)


# campaign_filepath = os.path.join(project_root, '../data/subtask_1038_20250410140715-0101-0331-campaign.csv')
# behavior_filepaths = ['/wkspace/qiushi/ltv-prediction/data/subtask_1066_20250414145246-0101-0110.csv', 
#                       '/wkspace/qiushi/ltv-prediction/data/task_123_20461_20250414223251-0111-0120.tar.gz', 
#                       '/wkspace/qiushi/ltv-prediction/data/task_124_20461_20250415072313-0121-0131.tar.gz']
# campaign_df_raw = load_campaign_data(campaign_filepath)
# behavior_df_raw = load_behavior_data(behavior_filepaths)

# keys, X_cols, y_cols, ignores = analyze_column(campaign_df_raw, verbose=False)
# campaign_df = add_device_price(campaign_df_raw[keys + X_cols + y_cols])
# behavior_df = flatten_behavior_data(behavior_df_raw, campaign_df)
# check_df(campaign_df)
# check_df(behavior_df)

# full_df = merge_behavior_campaign_data(behavior_df, campaign_df)
# check_df(full_df)
# print(full_df[~full_df['game_time_in_minutes_0'].isnull()].shape)
# print(full_df[full_df['game_time_in_minutes_0'].isnull()].shape)
# test_df = full_df[full_df['attribution_day'] <= '2025-01-29']

# print(test_df[~test_df['game_time_in_minutes_0'].isnull()].shape)
# print(test_df[test_df['game_time_in_minutes_0'].isnull()].shape)
# check_df(test_df)
# test_df1 = test_df.loc[:, ~test_df.columns.str.startswith('behav.')]

test_df2 = test_df.drop(['behav.total_recharge_amount_2', 'behav.total_recharge_amount_1', 'behav.total_recharge_amount_0', 'd1_retention', 'd2_retention', 'd3_retention'], axis=1)
check_df(test_df2)
check_df_data(test_df2)
# test_df2['pay_all'] = test_df2[[f'd{i}_payment' for i in range(1, 4)]].sum(axis=1)
# train_X, test_X, train_y, test_y, train_id_list, test_id_list = generate_train_test_data(test_df2, mode='regression', verbose=False, payment_only=False, split_time='2025-01-20')
train_X, test_X, train_y, test_y, train_id_list, test_id_list = generate_train_test_data(test_df2, mode='regression', verbose=False, payment_only=False)

# payment_cols = [f'd{i}_payment' for i in range(1, 8)]
# pprint(test_df2[payment_cols].values.tolist()[:200])



from sklearn.linear_model import LinearRegression, HuberRegressor, QuantileRegressor
from datetime import datetime
import time
import json
def save_model(model, filepath, train_X, cat_features, text_features):
    model.save_model(f"{filepath}.cbm")
    # 保存特征元数据
    metadata = {
        "feature_names": model.feature_names_,
        "feature_types": [str(train_X[col].dtype) for col in train_X.columns],
        "cat_features": cat_features,
        "text_features": text_features
    }
    print(metadata)
    with open(f"{filepath}_metadata.json", "w") as f:
        json.dump(metadata, f)

cat_features = train_X.select_dtypes(include=['object']).columns.tolist()
numberical_features = train_X.select_dtypes(include=['number']).columns.tolist()
# print(cat_features)
text_features = ['channel_ua', 'adtrace_device_ua']

# 训练、推理
train_y_baseline = train_X[['d1_payment', 'd2_payment', 'd3_payment']].sum(axis=1) * 1.06 
pred_y_baseline = test_X[['d1_payment', 'd2_payment', 'd3_payment']].sum(axis=1) * 1.06

# linear_model = LinearRegression(fit_intercept=False)
# linear_model.fit(train_X[numberical_features], train_y)
# print("linear model coef_:", linear_model.coef_)
# print("linear model intercept_:", linear_model.intercept_)
# train_y_base =  linear_model.predict(train_X[numberical_features])
# pred_y_base = linear_model.predict(test_X[numberical_features])
train_y_base = train_y_baseline
pred_y_base = pred_y_baseline

model = training(train_X, test_X, train_y - train_y_base, test_y - pred_y_base, mode='regression', eval_metric='MAE', loss_function='Lq:q=1.5',  
                 lr=1e-1, depth=4, iterations=3000, l2_leaf_reg=20, min_data_in_leaf=20, early_stopping_rounds=100, random_seed=42,
                 ignored_features=[
                     'adtrace_aid', 'adtrace_act_name', 'channel_ty_word_id', 'adtrace_device_ua', 'click_time', 'sdk_fileinittime',
                     # 'd1_payment', 'd2_payment', 'd3_payment',  'behav.recharge_count_0', 'behav.recharge_count_1', 'behav.recharge_count_2'
                 ],  
                 cat_features=cat_features, text_features=text_features)
save_model(model, filepath=f'catboost_{datetime.now().strftime("%Y%m%d-%H%M%S")}', 
           train_X=train_X, cat_features=cat_features, text_features=text_features)
pred_y = model.predict(test_X) + pred_y_base
pred_y = np.maximum(pred_y, 0)
    

test_y_campaign, pred_y_campaign, camp_count = merge_campaign(test_id_list, test_y, pred_y, ignore_compaigns=['unknown', '__DID__'])
_, pred_y_baseline_campaign, _ = merge_campaign(test_id_list, test_y, pred_y_baseline, ignore_compaigns=['unknown', '__DID__'])
# print(len(test_y), len(pred_y), len(test_y_campaign), len(pred_y_campaign), len(pred_y_baseline_campaign))
evaluation(test_y, pred_y_baseline, mode='regression', name='baseline by x1.06')
evaluation(test_y, pred_y, mode='regression', name='ours')
evaluation(test_y_campaign, pred_y_baseline_campaign, mode='regression', name='baseline by x1.06 (campaign)', verbose=False)
evaluation(test_y_campaign, pred_y_campaign, mode='regression', name='ours (campaign)', verbose=False)